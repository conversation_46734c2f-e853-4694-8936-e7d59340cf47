import { View } from 'react-native'
import { LoadingHico } from '../components/common/LoadingHico'
import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'

type MedicalFacultiesContentProps = {
  children?: React.ReactNode
}
export const MedicalFacultiesContent = ({ children }: MedicalFacultiesContentProps) => {
  const { isLoading } = useMedicalFacultiesStore()

  return (
    <View className="relative h-full w-full">
      <View className="flex h-full flex-col p-4">{children}</View>
      {isLoading && <LoadingHico />}
    </View>
  )
}
