'use client'
import { Text } from '@/components/ui/Text/Text'
import { useRouter } from 'expo-router'
import { TouchableOpacity, View } from 'react-native'

import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { HeaderSearch } from '../../components/search/HeaderSearch'
import { SearchFacultiesList } from '../../components/search/SearchFacultiesList'
import { MedicalFacultiesContent } from '../../layouts/MedicalFacultiesContent'

export const MedicalFacultiesSearchWrapper = () => {
  const { t } = useTranslation()

  const router = useRouter()

  const [initialKeywordSelected, setInitialKeywordSelected] = useState<Record<string, Keyword>>({})

  const { primaryLanguage } = useAppLanguage()

  const queryList = useMemo(() => {
    return Object.values(initialKeywordSelected).map(
      (keyword) =>
        (keyword.name as unknown as LocalizeField<string>)[primaryLanguage as LocaleEnum],
    )
  }, [initialKeywordSelected, primaryLanguage])

  const removeKeywordSelected = (keywordId: string) => {
    setInitialKeywordSelected((prev) => {
      const newSelected = { ...prev }
      delete newSelected[keywordId]
      return newSelected
    })
  }

  return (
    <View className="sticky top-0 z-10 bg-white ">
      <TouchableOpacity
        onPressIn={() => {
          if (router.canGoBack()) {
            router.back()
          } else {
            router.replace('/')
          }
        }}
        className="flex flex-row items-center justify-between gap-2 px-4 py-3 "
      >
        <ArrowLeftIcon width={16} height={16} />

        <Text size="body3" variant="primary">
          {t('MES-641')}
        </Text>

        <View className="h-[16px] w-[16px]"></View>
      </TouchableOpacity>

      <MedicalFacultiesContent>
        <HeaderSearch
          clearAllKeywordSelected={() => setInitialKeywordSelected({})}
          removeKeywordSelected={removeKeywordSelected}
          onApply={setInitialKeywordSelected}
          initialKeywordSelected={initialKeywordSelected}
        />

        <SearchFacultiesList query={queryList} />
      </MedicalFacultiesContent>
    </View>
  )
}
