import { Keyboard, TouchableWithoutFeedback, View } from 'react-native'

import { HeaderBarDetail } from '../../components/common/HeaderBarDetail'
import { MedicalFacultiesRecord } from '../../components/detail/MedicalFacultiesRecord'
import { MedicalFacultiesContent } from '../../layouts/MedicalFacultiesContent'

export const MedicalFacultyDetailWrapper = () => {

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <View className="sticky top-0 z-10 bg-white ">
        <HeaderBarDetail />

        <MedicalFacultiesContent>
          {/* <MedicalFacultiesDetailContent /> */}
          <MedicalFacultiesRecord />
        </MedicalFacultiesContent>

      </View>
    </TouchableWithoutFeedback>
  )
}
