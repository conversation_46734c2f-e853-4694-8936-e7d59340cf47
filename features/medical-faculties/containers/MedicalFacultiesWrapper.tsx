import ArrowLeftIcon from '@/assets/icons/arrow-left-primary.svg'
import { Text } from '@/components/ui/Text/Text'
import { useRouter } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { BoxContact } from '../components/BoxContact'
import { MedicalFacultyList } from '../components/MedicalFacultyList'
import { SearchFaculties } from '../components/SearchFaculties'
import { MedicalFacultiesContent } from '../layouts/MedicalFacultiesContent'

export const MedicalFacultyWrapper = () => {
  const { t } = useTranslation()

  const router = useRouter()

  return (
    <View className="h-full pb-6">
      <View className="sticky top-0 z-10 bg-white">
        <TouchableOpacity
          onPressIn={() => {
            if (router.canGoBack()) {
              router.back()
            } else {
              router.replace('/')
            }
          }}
          className="flex flex-row items-center gap-2 px-4 py-5"
        >
          <ArrowLeftIcon width={18} height={18} />

          <Text size="heading7" variant="primary">
            {t('MES-33')}
          </Text>
        </TouchableOpacity>
      </View>

      <MedicalFacultiesContent>
        <BoxContact />

        <SearchFaculties />

        <MedicalFacultyList />
      </MedicalFacultiesContent>
    </View>
  )
}
