import { TextInput } from '@/components/ui/TextInput/TextInput'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, View } from 'react-native'

import CloseIcon from '@/assets/icons/close-icon.svg'
import MicroPhoneIcon from '@/assets/icons/microphone-2.svg'
import MicroPhoneIconRed from '@/assets/icons/microphone-red.svg'
import { useLocalParticipant } from '@livekit/react-native'
import Animated, { useAnimatedStyle, useSharedValue } from 'react-native-reanimated'
import { useDataStreamTranscriptions } from '../../hooks/use-data-stream-transcription'
type DescriptionBoxProps = {
  isEditable?: boolean
  isAgentConnected: boolean
  onToggleConnection: () => void
}
export const DescriptionBox: React.FC<DescriptionBoxProps> = ({
  isEditable = true,
  isAgentConnected,
  onToggleConnection,
}) => {
  const { t } = useTranslation()
  const [value, setValue] = useState<string>('')

  const [isFocus, setIsFocus] = useState<boolean>(false)

  const pulseScale = useSharedValue(1)
  const pulseOpacity = useSharedValue(1)

  const animatedRingStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: pulseScale.value }],
      opacity: pulseOpacity.value,
    }
  })

  const { fullTranscript, clearTranscript } = useDataStreamTranscriptions()

  const { localParticipant, isMicrophoneEnabled } = useLocalParticipant()

  useEffect(() => {
    if (fullTranscript) {
      setValue(fullTranscript)
    }
  }, [fullTranscript])

  const handleToggleConnection = async () => {
    Keyboard.dismiss()
    if (!isAgentConnected) {
      onToggleConnection()
    } else {
      const enabled = !isMicrophoneEnabled
      await localParticipant.setMicrophoneEnabled(enabled)
    }
  }

  const handleClearTranscript = () => {
    clearTranscript()
    setValue('')
  }

  return (
    <View className="relative h-fit">
      <TextInput
        value={value}
        onChangeText={setValue}
        multiline
        wrapperClassName={`p-0 border-transparent`}
        className="w-full rounded-lg border border-transparent bg-[#F8F8FC] px-3 py-2 text-base text-custom-text focus:border-primary-500 focus:bg-white"
        placeholder={t('MES-1030')}
        editable={isEditable}
        style={{ maxHeight: 265, minHeight: 265, paddingBottom: 50 }}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
      ></TextInput>

      <View className="absolute bottom-4 right-4 z-10 flex-row items-center gap-3">
        {value && (
          <TouchableOpacity onPress={handleClearTranscript}>
            <CloseIcon width={32} height={32} />
          </TouchableOpacity>
        )}
        <TouchableOpacity onPress={handleToggleConnection}>
          <Animated.View
            style={[animatedRingStyle]}
            className={`size-16 rounded-full ${isFocus ? 'bg-[#F8F8FC]' : 'bg-white '} items-center justify-center`}
          >
            {!(isAgentConnected && isMicrophoneEnabled) ? (
              <MicroPhoneIcon width={24} height={24} />
            ) : (
              <MicroPhoneIconRed width={24} height={24} />
            )}
          </Animated.View>
        </TouchableOpacity>
      </View>
    </View>
  )
}
