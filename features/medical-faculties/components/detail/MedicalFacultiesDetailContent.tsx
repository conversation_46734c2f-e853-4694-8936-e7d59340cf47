import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { AudioSession, LiveKitRoom } from '@livekit/react-native'
import { useFocusEffect } from 'expo-router'
import { useCallback, useEffect, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useTranslateConnection } from '../../hooks/use-livekit-connection'
import { DescriptionBox } from './DescriptionBox'
import { HeaderDetail } from './HeaderDetail'
import { SuggestionTemplate } from './SuggestionTemplate'

export const MedicalFacultiesDetailContent = () => {
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const [isAgentConnected, setIsAgentConnected] = useState(false)

  // Start audio session
  useEffect(() => {
    const start = async () => {
      await AudioSession.startAudioSession()
    }
    start()
    return () => {
      AudioSession.stopAudioSession()
      setIsAgentConnected(false)
    }
  }, [])

  const { connectionDetails, isLoading, error, refetch } = useTranslateConnection({
    agent_name: 'transcribe-agent-dev',
    participant_name: user?.name || 'User',
    participant_identity: user?.email || user?.id || 'user-identity',
  })

  // Refetch connection details when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refetch()
    }, [refetch]),
  )

  return (
    <LiveKitRoom
      serverUrl={connectionDetails?.url}
      token={connectionDetails?.token}
      connect={isAgentConnected && !!connectionDetails?.token}
      audio={true}
      video={false}
    >
      <View className="h-full">
        <HeaderDetail />

        <Text size="body7" className="my-4">
          <Trans
            i18nKey="MES-998"
            components={{
              voice: (
                <Text size="body10" variant="primary">
                  {t('MES-999')}
                </Text>
              ),
            }}
          />{' '}
          :
        </Text>

        <DescriptionBox
          isAgentConnected={isAgentConnected}
          onToggleConnection={() => setIsAgentConnected((prev) => !prev)}
        />

        <SuggestionTemplate />
      </View>
    </LiveKitRoom>
  )
}
