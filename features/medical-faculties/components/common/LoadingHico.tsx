import { useEffect } from 'react'
import { View } from 'react-native'
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

import StartLoadingIcon from '@/assets/icons/starLoading.svg'
import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'

export const LoadingHico = () => {
  const { isLoading, showLoading, hiddenLoading } = useMedicalFacultiesStore()

  const { t } = useTranslation()

  const spin = useSharedValue(0)

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${spin.value}deg` }],
    }
  })

  useEffect(() => {
    spin.value = withRepeat(
      withTiming(360, {
        duration: 2000,
        easing: Easing.linear,
      }),
      -1,
      false,
    )
  }, [spin])

  return (
    isLoading && (
      <View className="absolute inset-0 flex h-full w-full flex-col-reverse justify-end">
        <View className="z-50 flex h-[350px] w-full items-center justify-center gap-3 bg-white">
          <Animated.View
            style={[
              {
                elevation: 10,

                shadowColor: '#8BB6EF',
                shadowOffset: {
                  width: -0.85,
                  height: 0,
                },
                shadowRadius: 17.07,
                shadowOpacity: 0.45,
              },
              animatedStyle,
            ]}
          >
            <StartLoadingIcon />
          </Animated.View>

          <Text size="body3">{t('MES-1031')}</Text>

          <Text size="body7" variant="subdued">
            {t('MES-1032')}
          </Text>
        </View>

        <View style={{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }} className="flex-1"></View>
      </View>
    )
  )
}
